import { useLayoutEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { gsap } from 'gsap';

// Interface pour les méthodes exposées du composant
export interface SunriseAnimationRef {
  triggerSunrise: () => void;
  triggerMorning: () => void;
  triggerMidday: () => void;
  triggerAfternoon: () => void;
  triggerSunset: () => void;
  triggerDawn: () => void;
  triggerDusk: () => void;
  triggerNight: () => void;
  resetSun: () => void;
}

// Interface pour les props du composant
interface SunriseAnimationProps {
  isVisible: boolean;
}

// 🔧 CISCO: TRAJECTOIRE PARABOLIQUE SOLAIRE - Gauche → Zénith → Droite
// Ligne d'horizon = 0° (milieu de l'écran)
// Valeurs négatives = sous l'horizon (caché), positives = au-dessus (visible)
const SUN_POSITIONS = {
  dawn: { angle: -30, horizontalOffset: -80 },      // CACHÉ sous horizon, extrême gauche
  sunrise: { angle: 10, horizontalOffset: -50 },    // À MOITIÉ VISIBLE, position Est
  morning: { angle: 60, horizontalOffset: -25 },    // Montée parabolique, gauche-centre
  midday: { angle: 90, horizontalOffset: 0 },       // ZÉNITH - Point le plus haut, centre
  afternoon: { angle: 60, horizontalOffset: 25 },   // Descente parabolique, centre-droite
  sunset: { angle: 10, horizontalOffset: 50 },      // À MOITIÉ VISIBLE, position Ouest
  dusk: { angle: -20, horizontalOffset: 70 },       // Sous horizon, droite
  night: { angle: -30, horizontalOffset: 80 }       // CACHÉ sous horizon, extrême droite
} as const;

// 🔧 CISCO: Fonction de conversion angle → position CSS
const angleToPosition = (angle: number, horizontalOffset: number = 0) => {
  // Conversion angle en position Y (0° = milieu écran)
  // -90° = 100% (tout en bas), +90° = -100% (tout en haut)
  const yPosition = -angle * (100 / 90); // Conversion linéaire
  
  // Position X basée sur l'offset horizontal
  const xPosition = horizontalOffset;
  
  return { y: `${yPosition}%`, x: `${xPosition}%` };
};

const SunriseAnimation = forwardRef<SunriseAnimationRef, SunriseAnimationProps>(
  ({ isVisible }, ref) => {
    // Références pour les éléments DOM
    const containerRef = useRef<HTMLDivElement>(null);
    const sunWrapperRef = useRef<HTMLDivElement>(null);
    const sunGlowRef = useRef<HTMLDivElement>(null);
    const lensFlareRef = useRef<HTMLDivElement>(null);
    const sunImageRef = useRef<HTMLImageElement>(null);

    // Référence pour la timeline GSAP
    const timelineRef = useRef<gsap.core.Timeline | null>(null);

    // 🌟 CISCO: Références pour animations continues (lens-flare + halo)
    const continuousAnimationsRef = useRef<(gsap.core.Timeline | gsap.core.Tween)[]>([]);

    // 🔧 CISCO: FONCTION UTILITAIRE - Calculer l'intensité du halo selon la hauteur
    const calculateGlowIntensity = (angle: number): number => {
      // SPÉCIFICATIONS : Caché (nuit/aube), à moitié visible (lever/coucher), haut (midi)
      if (angle <= 0) return 0; // CACHÉ - Sous l'horizon (nuit/aube)
      if (angle <= 15) return 0.6; // À MOITIÉ VISIBLE - Lever/coucher
      if (angle >= 90) return 1.2; // HAUT - Zénith (midi)

      // Interpolation linéaire entre 15° et 90°
      const ratio = (angle - 15) / (90 - 15); // 0 à 1
      return 0.6 + (ratio * 0.6); // 0.6 → 1.2
    };

    // 🔧 CISCO: FONCTION UTILITAIRE - Calculer l'intensité des rayons selon la hauteur
    const calculateFlareIntensity = (angle: number): number => {
      // SPÉCIFICATIONS : Caché (nuit/aube), à moitié visible (lever/coucher), haut (midi)
      if (angle <= 0) return 0; // CACHÉ - Sous l'horizon (nuit/aube)
      if (angle <= 15) return 0.3; // À MOITIÉ VISIBLE - Lever/coucher
      if (angle >= 90) return 0.7; // HAUT - Zénith (midi)

      // Interpolation linéaire entre 15° et 90°
      const ratio = (angle - 15) / (90 - 15); // 0 à 1
      return 0.3 + (ratio * 0.4); // 0.3 → 0.7
    };

    // 🌟 CISCO: NOUVELLE FONCTION - Arrêter toutes les animations continues
    const stopContinuousAnimations = () => {
      continuousAnimationsRef.current.forEach(animation => {
        if (animation) animation.kill();
      });
      continuousAnimationsRef.current = [];
    };

    // 🌟 CISCO: NOUVELLE FONCTION - Autorotation subtile du lens-flare
    const startLensFlareRotation = (intensity: number) => {
      if (!lensFlareRef.current) return;

      // Arrêter les animations précédentes
      stopContinuousAnimations();

      // Vitesse de rotation basée sur l'intensité (plus intense = plus lent pour plus de majesté)
      const rotationSpeed = intensity > 0.5 ? 120 : 80; // 120s pour forte intensité, 80s pour faible

      console.log(`🌟 Démarrage autorotation lens-flare - Intensité: ${intensity.toFixed(2)}, Vitesse: ${rotationSpeed}s`);

      const rotationAnimation = gsap.to(lensFlareRef.current, {
        rotation: 360,
        duration: rotationSpeed,
        ease: "none",
        repeat: -1
      });

      continuousAnimationsRef.current.push(rotationAnimation);
    };

    // 🌟 CISCO: NOUVELLE FONCTION - Animation pulsation subtile du halo
    const startHaloPulsation = (baseIntensity: number) => {
      if (!sunGlowRef.current) return;

      // Amplitude de pulsation basée sur l'intensité de base
      const minScale = 0.8 + (baseIntensity * 0.6);
      const maxScale = minScale + 0.15; // Pulsation subtile de 15%
      const pulseDuration = 8.0; // 8 secondes pour une pulsation très douce

      console.log(`💫 Démarrage pulsation halo - Base: ${baseIntensity.toFixed(2)}, Scale: ${minScale.toFixed(2)}-${maxScale.toFixed(2)}`);

      const pulsationAnimation = gsap.timeline({ repeat: -1, yoyo: true });

      pulsationAnimation.to(sunGlowRef.current, {
        scale: maxScale,
        duration: pulseDuration,
        ease: "power1.inOut"
      });

      continuousAnimationsRef.current.push(pulsationAnimation);
    };

    // 🔧 CISCO: FONCTION UTILITAIRE - Animer le soleil vers une position
    const animateSunToPosition = (
      targetPosition: keyof typeof SUN_POSITIONS,
      duration: number = 15.0,
      customGlowIntensity?: number,
      customFlareIntensity?: number
    ) => {
      if (!sunWrapperRef.current || !sunGlowRef.current || !lensFlareRef.current) {
        console.warn(`☀️ Éléments DOM non prêts pour l'animation ${targetPosition}`);
        return;
      }

      // Calculer l'intensité du halo et des rayons selon la hauteur du soleil
      const angle = SUN_POSITIONS[targetPosition].angle;
      const glowIntensity = customGlowIntensity ?? calculateGlowIntensity(angle);
      const flareIntensity = customFlareIntensity ?? calculateFlareIntensity(angle);

      console.log(`☀️ Animation vers ${targetPosition} - Angle: ${angle}°, Offset: ${SUN_POSITIONS[targetPosition].horizontalOffset}%, Halo: ${glowIntensity.toFixed(2)}, Rayons: ${flareIntensity.toFixed(2)}`);

      // Nettoyer l'animation précédente
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // 🌟 CISCO: Arrêter les animations continues précédentes
      stopContinuousAnimations();

      // Créer une nouvelle timeline
      timelineRef.current = gsap.timeline({
        onComplete: () => {
          console.log(`✨ Animation ${targetPosition} terminée - Position finale: ${angle}°, Halo final: ${glowIntensity.toFixed(2)}, Rayons finaux: ${flareIntensity.toFixed(2)}`);

          // 🌟 CISCO: Démarrer animations continues pour positions avec soleil visible
          if (['morning', 'midday', 'afternoon'].includes(targetPosition) && flareIntensity > 0) {
            console.log(`🌟 Démarrage animations continues pour ${targetPosition}`);
            startLensFlareRotation(flareIntensity);
            startHaloPulsation(glowIntensity);
          }
        }
      });

      // Calculer la position cible
      const targetPos = angleToPosition(angle, SUN_POSITIONS[targetPosition].horizontalOffset);

      console.log(`🎯 Position cible calculée: y=${targetPos.y}, x=${targetPos.x}, Halo adaptatif: ${glowIntensity.toFixed(2)}, Rayons adaptatifs: ${flareIntensity.toFixed(2)}`);

      // PHASE 1: Mouvement du soleil avec courbe parabolique
      timelineRef.current.to(
        sunWrapperRef.current,
        {
          y: targetPos.y,
          x: targetPos.x,
          duration: duration,
          ease: 'power2.inOut' // Courbe naturelle
        },
        0
      );

      // PHASE 2: Animation de la lueur synchronisée
      timelineRef.current.to(
        sunGlowRef.current,
        {
          opacity: glowIntensity,
          scale: 0.8 + (glowIntensity * 0.6), // Scale basé sur l'intensité
          duration: duration * 0.8,
          ease: 'power2.out'
        },
        duration * 0.1 // Démarre après 10% de la durée
      );

      // PHASE 3: Animation du lens flare
      timelineRef.current.to(
        lensFlareRef.current,
        {
          opacity: flareIntensity,
          duration: duration * 0.6,
          ease: 'power2.out'
        },
        duration * 0.2 // Démarre après 20% de la durée
      );
    };

    // 🌅 CISCO: Animation AUBE - Soleil CACHÉ sous l'horizon (-30°)
    const triggerDawn = () => {
      // CACHÉ : Soleil complètement invisible sous l'horizon
      animateSunToPosition('dawn', 15.0, 0, 0); // Aucun halo ni rayons
      // Masquer complètement le soleil
      if (sunWrapperRef.current) {
        gsap.set(sunWrapperRef.current, { opacity: 0 });
      }
    };

    // 🌅 CISCO: Animation LEVER DE SOLEIL - À MOITIÉ VISIBLE (10°)
    const triggerSunrise = () => {
      // À MOITIÉ VISIBLE : Soleil partiellement au-dessus de l'horizon
      animateSunToPosition('sunrise', 15.0, undefined, undefined); // Halo et rayons automatiques
      // Rendre le soleil visible
      if (sunWrapperRef.current) {
        gsap.set(sunWrapperRef.current, { opacity: 1 });
      }
    };

    // 🌄 CISCO: Animation MATIN - Montée parabolique (60°)
    const triggerMorning = () => {
      // VISIBLE : Soleil en montée parabolique
      animateSunToPosition('morning', 26.0, undefined, undefined);
      // Rendre le soleil visible
      if (sunWrapperRef.current) {
        gsap.set(sunWrapperRef.current, { opacity: 1 });
      }
    };

    // ☀️ CISCO: Animation MIDI/ZÉNITH - HAUT au zénith (90°)
    const triggerMidday = () => {
      // HAUT : Soleil au point le plus élevé
      animateSunToPosition('midday', 15.0, undefined, undefined);
      // Rendre le soleil visible
      if (sunWrapperRef.current) {
        gsap.set(sunWrapperRef.current, { opacity: 1 });
      }
    };

    // 🌇 CISCO: Animation APRÈS-MIDI - Descente parabolique (60°)
    const triggerAfternoon = () => {
      // VISIBLE : Soleil en descente parabolique
      animateSunToPosition('afternoon', 26.0, undefined, undefined);
      // Rendre le soleil visible
      if (sunWrapperRef.current) {
        gsap.set(sunWrapperRef.current, { opacity: 1 });
      }
    };

    // 🌇 CISCO: Animation COUCHER DE SOLEIL - À MOITIÉ VISIBLE (10°)
    const triggerSunset = () => {
      // À MOITIÉ VISIBLE : Soleil partiellement au-dessus de l'horizon
      animateSunToPosition('sunset', 22.0, undefined, undefined); // Descente progressive
      // Rendre le soleil visible
      if (sunWrapperRef.current) {
        gsap.set(sunWrapperRef.current, { opacity: 1 });
      }
    };

    // 🌆 CISCO: Animation CRÉPUSCULE - Soleil sous l'horizon (-20°)
    const triggerDusk = () => {
      animateSunToPosition('dusk', 15.0, 0, 0); // Pas de halo ni rayons sous l'horizon
    };

    // 🌌 CISCO: Animation NUIT PROFONDE - Soleil CACHÉ (-30°)
    const triggerNight = () => {
      // CACHÉ : Soleil complètement invisible sous l'horizon
      animateSunToPosition('night', 15.0, 0, 0); // Aucun halo ni rayons
      // Masquer complètement le soleil
      if (sunWrapperRef.current) {
        gsap.set(sunWrapperRef.current, { opacity: 0 });
      }
    };

    // 🔄 CISCO: Remettre le soleil en position initiale
    const resetSun = () => {
      if (timelineRef.current) {
        timelineRef.current.kill();
      }

      // 🌟 CISCO: Arrêter toutes les animations continues
      stopContinuousAnimations();

      if (sunWrapperRef.current && sunGlowRef.current && lensFlareRef.current) {
        const initialPos = angleToPosition(SUN_POSITIONS.dawn.angle, SUN_POSITIONS.dawn.horizontalOffset);

        gsap.set(sunWrapperRef.current, {
          y: initialPos.y,
          x: initialPos.x,
          opacity: 1
        });
        gsap.set(sunGlowRef.current, {
          opacity: 0,
          scale: 0.8
        });
        gsap.set(lensFlareRef.current, {
          opacity: 0,
          rotation: 0
        });
      }

      console.log('🔄 Soleil remis en position initiale (aube)');
    };

    // Exposer les méthodes via useImperativeHandle
    useImperativeHandle(ref, () => ({
      triggerSunrise,
      triggerMorning,
      triggerMidday,
      triggerAfternoon,
      triggerSunset,
      triggerDawn,
      triggerDusk,
      triggerNight,
      resetSun
    }));

    // Cleanup à la destruction du composant
    useLayoutEffect(() => {
      return () => {
        if (timelineRef.current) {
          timelineRef.current.kill();
        }
        // 🌟 CISCO: Nettoyer les animations continues
        stopContinuousAnimations();
      };
    }, []);

    // Ne pas rendre si non visible
    if (!isVisible) {
      return null;
    }

    return (
      <div
        ref={containerRef}
        className="fixed inset-0 w-full h-full pointer-events-none"
        style={{ zIndex: 11 }} // 🔧 CISCO: Z-index élevé pour être visible devant le paysage
      >
        {/* Conteneur pour le soleil et ses effets */}
        <div
          ref={sunWrapperRef}
          className="absolute w-52 h-52 left-1/2 top-1/2 -translate-x-1/2"
          style={{
            transform: 'translateX(-50%) translateY(50%)', // Position initiale visible pour test
            opacity: 1, // 🔧 CISCO: Visible par défaut pour débogage
          }}
        >
          <div className="relative w-full h-full">
            {/* EFFET 1: Lueur subtile du soleil */}
            <div
              ref={sunGlowRef}
              className="sun-glow absolute inset-0 opacity-0"
            />
            
            {/* EFFET 2: Soleil principal */}
            <img
              ref={sunImageRef}
              src="/SUN.png"
              alt="Soleil"
              className="w-full h-full object-contain"
              style={{
                filter: 'brightness(1.5) saturate(1.3)', // 🔧 CISCO: Plus lumineux pour visibilité
                zIndex: 12, // 🔧 CISCO: Z-index élevé pour être au premier plan
                opacity: 1 // 🔧 CISCO: Opacité forcée pour débogage
              }}
            />

            {/* EFFET 3: Lens Flare */}
            <div
              ref={lensFlareRef}
              className="absolute opacity-0 pointer-events-none"
              style={{
                width: '600px',
                height: 'auto',
                left: '50%',
                top: '50%',
                transform: 'translate(-50%, -50%)',
                transformOrigin: 'center center',
                zIndex: 15
              }}
            >
              <img
                src="/lens-flare-light-3508x2540.png"
                alt="Lens Flare"
                className="w-full h-auto"
                style={{
                  // 🔧 CISCO: TECHNIQUE INCRUSTATION - Colorise uniquement les pixels visibles
                  filter: 'sepia(100%) saturate(200%) hue-rotate(30deg) brightness(1.8) contrast(1.2)'
                }}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }
);

SunriseAnimation.displayName = 'SunriseAnimation';

export default SunriseAnimation;
