import React, { useEffect, useRef } from 'react';
import NewStars from './NewStars'; // 🔧 CISCO: Nouveau composant étoiles simplifié
import MoonAnimation from '../UI/MoonAnimation';
import SunriseAnimation, { SunriseAnimationRef } from './SunriseAnimation'; // 🔧 CISCO: Soleil intégré

// Interface pour les props du composant
interface AstronomicalLayerProps {
  // Mode du ciel pour contrôler la visibilité des étoiles
  skyMode?: 'night' | 'dawn' | 'sunrise' | 'morning' | 'midday' | 'afternoon' | 'sunset' | 'dusk';
}

const AstronomicalLayer: React.FC<AstronomicalLayerProps> = ({ skyMode = 'night' }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sunriseAnimationRef = useRef<SunriseAnimationRef>(null); // 🔧 CISCO: Référence pour le soleil








  // 🔧 CISCO: Suppression du double système d'étoiles - NewStars s'occupe de tout
  useEffect(() => {
    console.log(`🌌 AstronomicalLayer: Mode ${skyMode} - Délégation à NewStars`);

    // 🔧 CISCO: Exposer les fonctions du soleil globalement
    if (sunriseAnimationRef.current) {
      (window as any).triggerSunriseAnimation = sunriseAnimationRef.current.triggerSunrise;
      (window as any).triggerMorningAnimation = sunriseAnimationRef.current.triggerMorning;
      (window as any).triggerMiddayAnimation = sunriseAnimationRef.current.triggerMidday;
      (window as any).triggerAfternoonAnimation = sunriseAnimationRef.current.triggerAfternoon;
      (window as any).triggerSunsetAnimation = sunriseAnimationRef.current.triggerSunset;
      (window as any).triggerDawnAnimation = sunriseAnimationRef.current.triggerDawn;
      (window as any).triggerDuskAnimation = sunriseAnimationRef.current.triggerDusk;
      (window as any).triggerNightSunAnimation = sunriseAnimationRef.current.triggerNight;
      console.log('☀️ Fonctions soleil exposées globalement depuis AstronomicalLayer');
    }
  }, [skyMode]);

  return (
    <div
      ref={containerRef}
      className="fixed inset-0 pointer-events-none"
      style={{
        zIndex: 8 // 🔧 CISCO: Couche astronomique (étoiles z-7 + lune z-8) - VERROUILLÉ
      }}
    >
      {/* 🌟 CISCO: Nouvelles étoiles simplifiées avec haute densité */}
      <NewStars skyMode={skyMode} density="high" />

      {/* ☀️ CISCO: Soleil intégré dans la couche astronomique - DERRIÈRE les nuages */}
      <SunriseAnimation
        ref={sunriseAnimationRef}
        isVisible={true}
      />

      {/* 🌙 CISCO: Lune intégrée dans la couche astronomique pour ordre DOM correct */}
      <MoonAnimation
        isNightMode={skyMode === 'night'}
        currentMode={skyMode}
      />
    </div>
  );
};

export default AstronomicalLayer;
